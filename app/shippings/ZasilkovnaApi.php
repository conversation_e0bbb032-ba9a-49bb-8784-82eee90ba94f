<?php

/**
* Zasilkovna API 
* http://docs.ulozenkav3.apiary.io/
* <AUTHOR>
*/
class ZasilkovnaException extends \Exception { }

class ZasilkovnaApi {
  const API_URL = 'https://www.zasilkovna.cz/api/soap-php-bugfix.wsdl';

  const POINTS_URL_JSON = 'https://www.zasilkovna.cz/api/v4/';
  
  private $apiPassword = '';
  private $eshopName = '';
  private $apiKey = '';
  private $apiPointsUrl = '';
  private $countries = array('cz'=>'cz', 'sk'=>'sk');

  private $gw;
  
  public $errMsg = "";

  /** @var ModelFactory */
  protected ModelFactory $model;
  
  public function __construct($config, $model) {
    $this->model = $model;

    $this->apiPassword = $config["passw"];
    $this->apiKey = $config["key"];
    $this->eshopName = $config["eshopName"];
    $this->apiPointsUrl = self::POINTS_URL_JSON;

    $this->gw = new SoapClient(self::API_URL);

  }  
  
  /**
  * vraci TRUE/FALSE zda je sluzba aktivni
  * 
  */
  public function isApiOn(): bool {
    return (TRUE);    
  }

  /**
   * POST consignments
   * /v3/consignments{?timeFrom,updatedFrom,limit,offset}
   *
   * Vytvoření nové zásilky
   *
   * @param $order
   * @return int parcelId
   * @throws \Dibi\Exception
   */
  public function postParcelAdd($order) {
    if (!empty($order->ordparcode)) {
      $this->errMsg = "Objednávka č. ".$order->ordcode." už byla odeslaná/má přiřazené číslo balíku.";
      return FALSE;
    }
     
    $phone = trim($order->ordtel);
    $phone = str_replace(' ', '', $phone);

    $phonePrefix = $order->ordicouid == 1 ? '+420' : '+421';

    if (!empty($phone)) {
      $phone = $phonePrefix . substr($phone, -9);
    }
    //nactu zpusob platby
    $payMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    $delMode = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $payMode->delmasid);
    //pokud je jina dodaci adresa
    if (!empty($order->ordstname) && !empty($order->ordstlname)) {
      $order->ordiname = $order->ordstname; 
      $order->ordilname = $order->ordstlname; 
      $order->ordifirname = $order->ordstfirname; 
      $order->ordistreet = $order->ordststreet; 
      $order->ordistreetno = $order->ordststreetno; 
      $order->ordicity = $order->ordstcity; 
      $order->ordipostcode = $order->ordstpostcode;
      $order->ordicouid = $order->ordstcouid;
    }

    $ordweight = 2;
    $parcelsCount = (int)$order->ordparcelscount > 0 ? (int)$order->ordparcelscount : 1;
    if (!empty($order->ordweight)) {
      $ordweight = round((double)$order->ordweight/$parcelsCount, 1);
    }

    $arr = array(
      'number' => (string)$order->ordcode,
      'name' => (string)$order->ordiname,
      'surname' => (string)$order->ordilname,
      'company' => (string)$order->ordifirname,
      'email' => (string)$order->ordmail,
      'phone' => (string)$phone,
      'addressId' => $order->orddelspec,
      'value' => (double)$order->ordpricevat,
      'currency' => ($order->ordicouid == 1 ? 'CZK' : 'EUR'),
      'weight' => $ordweight,
      'eshop' => $this->eshopName,
    );

    if ($delMode->delcode == "ZASILKOVNA_NA_ADRESU") {
      $arr["addressId"] = $order->ordicouid == 1 ? 106 : 131;
      $arr["street"] = $order->ordistreet;
      $arr["houseNumber"] = $order->ordistreetno;
      $arr["city"] = $order->ordicity;
      $arr["zip"] = $order->ordipostcode;
    }

    //kontrola povinnych poli
    $emptyFields = array();
    if (empty($arr["number"])) $emptyFields[] = "Kód obj.";
    if (empty($arr["name"])) $emptyFields[] = "Jméno";
    if (empty($arr["surname"])) $emptyFields[] = "Příjmení";
    if (empty($arr["email"])) $emptyFields[] = "Email";
    if (empty($arr["phone"])) $emptyFields[] = "Telefon";
    if (empty($arr["addressId"]) && $delMode->delcode == "ZASILKOVNA")  $emptyFields[] = "ID odběrného místa";
    if (empty($arr["value"])) $emptyFields[] = "Cena";
    if (count($emptyFields)) {
      $this->errMsg = "Nejsou vyplněny tyto povinné pole:".implode(", ", $emptyFields);
      return false;
    }

    if ($payMode->delcode == 'dobirka' || $payMode->delcode == 'cash') {
      if (!empty($order->ordpricecod)) {
        $arr["cod"] = (double)$order->ordpricecod;
      } else {
        $arr["cod"] = (double)$order->ordpricevat;
      }
    }

    try {
      $packet = $this->gw->createPacket($this->apiPassword, $arr);
      if (isset($packet->id)) {
        return (string)$packet->id;
      }

    } catch(SoapFault $e) {
      $message = $e->getMessage();
      if (!empty($e->detail->PacketAttributesFault->attributes->fault)) {
        $message = implode(", ", (array)$e->detail->PacketAttributesFault->attributes->fault);
      }
      $this->errMsg = "postParcelAdd error. Kód obj.=" . $order->ordcode . " : ".$message;
      return FALSE;
    }

    return TRUE;
  }

  /**
   * @param $consignments
   * @param $format
   * @param $firstPosition
   * @return false
   */
  public function postLabels($consignments, $format, $firstPosition): bool {
    if (empty($format)) $format = 'A7 on A4';
    if (empty($firstPosition)) $firstPosition = 0;
    
    try {
      $consignmentsStr = implode("", $consignments);      
      $file = $this->gw->packetsLabelsPdf($this->apiPassword, $consignments, $format, $firstPosition);
      $this->downloadPdf($file);

    } catch(SoapFault $e) {
      $message = $e->getMessage();
      if (!empty($e->detail->PacketAttributesFault->attributes->fault)) {
        $message = implode(", ", (array)$e->detail->PacketAttributesFault->attributes->fault);
      }
      $this->errMsg = "postParcelAdd error: ".$message;
      return FALSE;
    }
    return true;
  }

  private function downloadPdf($file, $fileName='stitky') {
    @unlink(TEMP_DIR . '/print.pdf');
    file_put_contents(TEMP_DIR . '/print.pdf', $file);

    header("Content-type:application/pdf");
    header("Content-Disposition:attachment;filename=$fileName.pdf");
    readfile(TEMP_DIR . '/print.pdf');

    /*
    header('Content-Disposition: attachment;filename="dpd_' . $fileName . '.pdf"');
    header('Content-Type: application/force-download');
    header('Content-Length: ' . (strlen($file)));
    ob_flush();
    flush();
    */

    //echo $file;
  }

  public function updateBranches(): void {

    $start = dibi::fetchSingle("SELECT NOW()");

    $zass = $this->model->getZasilkovnapointsModel();

    //načtu místa
    $date = new DateTime();
    $url = self::POINTS_URL_JSON.$this->apiKey."/branch.xml?lang=cs" ;
    $xmlFile = WWW_DIR . '/../data/zasilkovna_branches.xml';
    copy($url, $xmlFile);
    $xml = new \XMLReader();
    if (!$xml->open($xmlFile)) {
      throw new \Exception("Zdrojový soubor se nepodařilo načíst");
    }

    //pro které země inportovat
    $countries = array('cz'=>'cz', 'sk'=>'sk');

    // Načteme všechny existující záznamy do paměti pro rychlejší vyhledávání
    $existingRecords = dibi::fetchPairs("SELECT zasid2, zasid FROM zasilkovnapoints");

    $batchData = array();
    $batchSize = 100; // Zpracováváme po dávkách
    $processedCount = 0;

    while ($xml->read()) {
      if ($xml->nodeType == \XMLReader::ELEMENT && $xml->name == 'branch') {
        $xmlElement = $xml->readOuterXml();
        $row = simplexml_load_string($xmlElement, 'SimpleXMLElement', LIBXML_NOBLANKS && LIBXML_NOWARNING);

        //jen nastavené země
        if (!array_key_exists((string)$row->country, $countries)) {
          // Explicitně uvolníme paměť
          unset($row, $xmlElement);
          continue;
        }

        /*
        $openingHours = "";
        if (isset($row->openingHours->regular->monday)) {
          $hours = $row->openingHours->regular->monday;
          if (is_string($hours)) $openingHours .= "Pondělí: $hours <br>";
        }
        if (isset($row->openingHours->regular->tuesday)) {
          $hours = $row->openingHours->regular->tuesday;
          if (is_string($hours)) $openingHours .= "Úterý: $hours <br>";
        }
        if (isset($row->openingHours->regular->wednesday)) {
          $hours = $row->openingHours->regular->wednesday;
          if (is_string($hours)) $openingHours .= "Středa: $hours <br>";
        }
        if (isset($row->openingHours->regular->thursday)) {
          $hours = $row->openingHours->regular->thursday;
          if (is_string($hours)) $openingHours .= "Čtvrtek: $hours <br>";
        }
        if (isset($row->openingHours->regular->friday)) {
          $hours = $row->openingHours->regular->friday;
          if (is_string($hours)) $openingHours .= "Pátek: $hours <br>";
        }
        if (isset($row->openingHours->regular->saturday)) {
          $hours = $row->openingHours->regular->saturday;
          if (is_string($hours)) $openingHours .= "Sobota: $hours <br>";
        }
        if (isset($row->openingHours->regular->sunday)) {
          $hours = $row->openingHours->regular->sunday;
          if (is_string($hours)) $openingHours .= "Neděle: $hours <br>";
        }
        */

        $navigation = "";
        if (!empty($row->directions) && is_string((string)$row->directions)) $navigation .= $row->directions . "<br>";
        if (!empty($row->directionsCar) && is_string((string)$row->directionsCar)) $navigation .= "Autem: " . $row->directionsCar . "<br>";
        if (!empty($row->directionsPublic) && is_string((string)$row->directionsPublic)) $navigation .= "Hromadná doprava: " . $row->directionsPublic . "<br>";

        $rowId = (string)$row->id;
        $data = array(
          'zasid2' => $rowId,
          'zasname' => (string)$row->place . ', ' . (string)$row->name,
          'zasstreet' => (string)$row->street,
          'zascity' => (string)$row->city,
          'zaspostcode' => (string)$row->zip,
          'zascountry' => (string)$row->country,
          //'zasopeninghours' => $openingHours,
          'zasnavigation' => $navigation,
          'zasstatus' => 0,
          'zasurl' => (string)$row->url,
          'zasurlphoto' => !empty($row->photos[0]->normal) ? (string)$row->photos[0]->normal : "",
          'zasgpsn' => (string)$row->latitude,
          'zasgpse' => (string)$row->longitude,
        );

        // Použijeme předem načtené záznamy místo databázového dotazu
        if (isset($existingRecords[$rowId])) {
          $data['action'] = 'update';
          $data['zasid'] = $existingRecords[$rowId];
          unset($data["zasid2"]);
        } else {
          $data['action'] = 'insert';
        }

        $batchData[] = $data;
        $processedCount++;

        // Explicitně uvolníme paměť
        unset($row, $xmlElement, $navigation, $rowId);

        // Zpracujeme dávku
        if (count($batchData) >= $batchSize) {
          $this->processBatch($batchData, $zass);
          $batchData = array();

          // Vynucené uvolnění paměti
          if ($processedCount % 500 == 0) {
            gc_collect_cycles();
          }
        }
      }
		}

		// Zpracujeme zbývající záznamy
    if (!empty($batchData)) {
      $this->processBatch($batchData, $zass);
    }

    // Uzavřeme XMLReader
    $xml->close();
    unset($xml, $existingRecords, $batchData);

    $cnt =dibi::query("UPDATE zasilkovnapoints SET zasstatus=1 WHERE coalesce(zasdateu, zasdatec)<%dt", $start);
  }

  /**
   * Zpracuje dávku záznamů pro optimalizaci paměti
   */
  private function processBatch($batchData, $zass) {
    foreach ($batchData as $data) {
      if ($data['action'] === 'update') {
        $zasId = $data['zasid'];
        unset($data['action'], $data['zasid']);
        $zass->update($zasId, $data);
      } else {
        unset($data['action']);
        $zass->insert($data);
      }
    }
  }

  public function getParcelStatus($packetId) {
    try {
      return $this->gw->packetStatus($this->apiPassword, $packetId);
    }
    catch (SoapFault $e){
      $this->errMsg = "packetStatus error: ".$e->getMessage();
      return FALSE;
    }
  }
}
