<!DOCTYPE html><!-- "' --></textarea></script></style></pre></xmp></a></iframe></noembed></noframes></noscript></option></select></template></title></table>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">
	<meta name="generator" content="Tracy by Nette Framework">

	<title>Fatal Error: Allowed memory size of 268435456 bytes exhausted (tried to allocate 16384 bytes)</title>
	<!-- in /www/hosting/goldfitness.cz/vendor/dibi/dibi/src/Dibi/Drivers/MySqliDriver.php:149 -->
	
	<style class="tracy-debug">
	/** * This file is part of the Tracy (https://tracy.nette.org) */ #tracy-bs { font: 9pt/1.5 Verdana, sans-serif; background: white; color: #333; position: absolute; z-index: 20000; left: 0; top: 0; width: 100%; text-align: left; } #tracy-bs a { text-decoration: none; color: #328ADC; padding: 2px 4px; margin: -2px -4px; } #tracy-bs a:hover, #tracy-bs a:focus { color: #085AA3; } #tracy-bs-toggle { position: absolute; right: .5em; top: .5em; text-decoration: none; background: #CD1818; color: white !important; padding: 3px; } #tracy-bs-error { background: #CD1818; color: white; font-size: 13pt; } #tracy-bs-error a { color: white !important; opacity: 0; font-size: .7em; border-bottom: none !important; } #tracy-bs-error:hover a { opacity: .6; } #tracy-bs-error a:hover { opacity: 1; } #tracy-bs-error i { color: #ffefa1; font-style: normal; } #tracy-bs h1 { font-size: 15pt; font-weight: normal; text-shadow: 1px 1px 2px rgba(0, 0, 0, .3); margin: .7em 0; } #tracy-bs h1 span { white-space: pre-wrap; } #tracy-bs h2 { font-size: 14pt; font-weight: normal; margin: .6em 0; } #tracy-bs h3 { font-size: 10pt; font-weight: bold; margin: 1em 0; padding: 0; } #tracy-bs p, #tracy-bs pre { margin: .8em 0 } #tracy-bs pre, #tracy-bs code, #tracy-bs table { font: 9pt/1.5 Consolas, monospace !important; } #tracy-bs pre, #tracy-bs table { background: #FDF5CE; padding: .4em .7em; border: 1px dotted silver; overflow: auto; } #tracy-bs table pre { padding: 0; margin: 0; border: none; } #tracy-bs table { border-collapse: collapse; width: 100%; margin-bottom: 1em; } #tracy-bs td, #tracy-bs th { vertical-align: top; text-align: left; padding: 2px 6px; border: 1px solid #e6dfbf; } #tracy-bs th { font-weight: bold; } #tracy-bs tr > :first-child { width: 20%; } #tracy-bs tr:nth-child(2n), #tracy-bs tr:nth-child(2n) pre { background-color: #F7F0CB; } #tracy-bs ol { margin: 1em 0; padding-left: 2.5em; } #tracy-bs ul { font-size: 7pt; padding: 2em 3em; margin: 1em 0 0; color: #777; background: #F6F5F3; border-top: 1px solid #DDD; list-style: none; } #tracy-bs-logo a { position: absolute; bottom: 0; right: 0; width: 100px; height: 50px; background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUBAMAAAD/1DctAAAAMFBMVEWupZzj39rEvbTy8O3X0sz9/PvGwLu8tavQysHq6OS0rKP5+Pbd2dT29fPMxbzPx8DKErMJAAAACXBIWXMAAAsTAAALEwEAmpwYAAACGUlEQVQoFX3TQWgTQRQA0MWLIJJDYehBTykhG5ERTx56K1u8eEhCYtomE7x5L4iLh0ViF7egewuFFqSIYE6hIHsIYQ6CQSg9CDKn4QsNCRlB59C74J/ZNHW1+An5+bOPyf6/s46oz2P+A0yIeZZ2ieEHi6TOnLKTxvWq+b52mxlVO3xnM1s7xLX1504XQH65OnW2dBqn7cCkYsFsfYsWpyY/2salmFTpEyzeR8zosYqMdiPDXdyU52K1wgEa/SjGpdEwUAxqvRfckQCDOyFearsEHe2grvkh/cFAHKvdtI3lcVceKQIOFpv+FOZaNPQBwJZLPp+hfrvT5JZXaUFsR8zqQc9qSgAharkfS5M/5F6nGJJAtXq/eLr3ucZpHccSxOOIPaQhtHohpCH2Xu6rLmQ0djnr4/+J3C6v+AW8/XWYxwYNdlhWj/P5fPSTQwVr0T9lGxdaBCqErNZaqYnEwbkjEB3NasGF3lPdrHa1nnxNOMgj0+neePUPjd2v/qVvUv29ifvc19huQ48qwXShy/9o8o3OSk0cs37mOFd0Ydgvsf/oZEnPVtggfd66lORn9mDyyzXU13SRtH2L6aR5T/snGAcZPfAXz5J1YlJWBEuxdMYqQecpBrlM49xAbmqyHA+xlA1FxBtqT2xmJoNXZlIt74ZBLeJ9ZGDqByNI7p543idzJ23vXEv7IgnsxiS+eNtwNbFdLq7+Bi4wQ0I4SVb9AAAAAElFTkSuQmCC') no-repeat; opacity: .6; padding: 0; margin: 0; } #tracy-bs-logo a:hover, #tracy-bs-logo a:focus { opacity: 1; transition: opacity 0.1s; } #tracy-bs div.panel { padding: 1px 25px; } #tracy-bs div.inner { background: #F4F3F1; padding: .1em 1em 1em; border-radius: 8px; } #tracy-bs .outer { overflow: auto; } #tracy-bs.mac .outer { padding-bottom: 12px; } /* source code */ #tracy-bs pre.code > div { min-width: 100%; float: left; white-space: pre; } #tracy-bs .highlight { background: #CD1818; color: white; font-weight: bold; font-style: normal; display: block; padding: 0 .4em; margin: 0 -.4em; } #tracy-bs .line { color: #9F9C7F; font-weight: normal; font-style: normal; } #tracy-bs pre:hover span[title] { border-bottom: 1px solid rgba(0, 0, 0, 0.2); } #tracy-bs a[href^=editor\:] { color: inherit; border-bottom: 1px dotted rgba(0, 0, 0, .3); } #tracy-bs span[data-tracy-href] { border-bottom: 1px dotted rgba(0, 0, 0, .3); } #tracy-bs .tracy-dump-whitespace { color: #0003; } #tracy-bs .caused { float: right; padding: .3em .6em; background: #df8075; border-radius: 0 0 0 8px; white-space: nowrap; } #tracy-bs .caused a { color: white; } #tracy-bs .args tr:first-child > * { position: relative; } #tracy-bs .args tr:first-child td:before { position: absolute; right: .3em; content: 'may not be true'; opacity: .4; } /** * This file is part of the Tracy (https://tracy.nette.org) */ .tracy-collapsed { display: none; } .tracy-toggle.tracy-collapsed { display: inline; } .tracy-toggle { cursor: pointer; -webkit-user-select: none; -moz-user-select: none; -khtml-user-select: none; -ms-user-select: none; user-select: none; } .tracy-toggle:after { content: "\A0\25BC"; opacity: .4; } .tracy-toggle.tracy-collapsed:after { content: "\A0\25BA"; } /** * This file is part of the Tracy (https://tracy.nette.org) */ .tracy-sortable > :first-child > tr:first-child > * { position: relative; } .tracy-sortable > :first-child > tr:first-child > *:hover:before { position: absolute; right: .3em; content: "\21C5"; opacity: .4; font-weight: normal; } /** * This file is part of the Tracy (https://tracy.nette.org) */ pre.tracy-dump { text-align: left; color: #444; background: white; } pre.tracy-dump div { padding-left: 3ex; } pre.tracy-dump div div { border-left: 1px solid rgba(0, 0, 0, .1); margin-left: .5ex; } pre.tracy-dump a { color: #125EAE; text-decoration: none; } pre.tracy-dump a:hover, pre.tracy-dump a:focus { background-color: #125EAE; color: white; } .tracy-dump-array, .tracy-dump-object { color: #C22; } .tracy-dump-string { color: #35D; } .tracy-dump-number { color: #090; } .tracy-dump-null, .tracy-dump-bool { color: #850; } .tracy-dump-visibility, .tracy-dump-hash { font-size: 85%; color: #999; } .tracy-dump-indent { display: none; } span[data-tracy-href] { border-bottom: 1px dotted rgba(0, 0, 0, .2); } .tracy-dump-flash { animation: tracy-dump-flash .2s ease; } @keyframes tracy-dump-flash { 0% { background: #c0c0c033; } } 	</style>
	<script>document.documentElement.className+=' tracy-js'</script>
</head>


<body>
<div id="tracy-bs" itemscope>
	<a id="tracy-bs-toggle" href="#" class="tracy-toggle"></a>
	<div>
		<div id="tracy-bs-error" class="panel">
			<p>Fatal Error</p>

			<h1><span>Allowed memory size of 268435456 bytes exhausted (tried to allocate 16384 bytes)</span>
			</h1>
		</div>

		

				
			

			

						
			<div class="panel">
			<h2><a data-tracy-ref="^+" class="tracy-toggle">Source file</a></h2>

			<div class="inner">
				<p><b>File:</b> <a href="editor://open/?file=%2Fwww%2Fhosting%2Fgoldfitness.cz%2Fvendor%2Fdibi%2Fdibi%2Fsrc%2FDibi%2FDrivers%2FMySqliDriver.php&amp;line=149&amp;search=&amp;replace=" title="/www/hosting/goldfitness.cz/vendor/dibi/dibi/src/Dibi/Drivers/MySqliDriver.php:149">.../dibi/src/Dibi/Drivers/<b>MySqliDriver.php</b>:149</a></p>
				<pre data-tracy-href="editor://open/?file=%2Fwww%2Fhosting%2Fgoldfitness.cz%2Fvendor%2Fdibi%2Fdibi%2Fsrc%2FDibi%2FDrivers%2FMySqliDriver.php&amp;line=149&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>139:</span>    
<span class='line'>140:</span>    
<span class='line'>141:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>142:</span>         * Executes the SQL query.
<span class='line'>143:</span>         * @param  string      SQL statement.
<span class='line'>144:</span>         * @return Dibi\ResultDriver|null
<span class='line'>145:</span>         * @throws Dibi\DriverException
<span class='line'>146:</span>         */
<span class='line'>147:</span>        </span><span style="color: #D24; font-weight: bold">public function </span><span style="color: #000">query</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$sql</span><span style="color: #D24; font-weight: bold">)
<span class='line'>148:</span>        {
<span class='highlight'>149:            $res = @mysqli_query($this-&gt;connection, $sql, $this-&gt;buffered ? MYSQLI_STORE_RESULT : MYSQLI_USE_RESULT); // intentionally @
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #998; font-style: italic"><span class='line'>150:</span>    
<span class='line'>151:</span>            </span><span style="color: #D24; font-weight: bold">if (</span><span style="color: #000">$code </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">mysqli_errno</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">connection</span><span style="color: #D24; font-weight: bold">)) {
<span class='line'>152:</span>                throw static::</span><span style="color: #000">createException</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">mysqli_error</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">connection</span><span style="color: #D24; font-weight: bold">), </span><span style="color: #000">$code</span><span style="color: #D24; font-weight: bold">, </span><span style="color: #000">$sql</span><span style="color: #D24; font-weight: bold">);
<span class='line'>153:</span>    
</span></span></code></div></pre>			</div></div>


						

			
				

				<div class="panel">
		<h2><a data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">Exception</a></h2>
		<div class="tracy-collapsed inner">
		<pre class="tracy-dump" data-tracy-dump='{"object":1}'></pre>
		</div></div>
		

		

				